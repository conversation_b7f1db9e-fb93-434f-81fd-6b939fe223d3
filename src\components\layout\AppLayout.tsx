
import { useNavigate, useLocation } from "react-router-dom";
import { SidebarProvider, Sidebar, SidebarHeader, SidebarContent, SidebarFooter, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarMenuSubButton, SidebarTrigger } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { LogOut, Home, Users, LineChart, Settings, User, Database, FileCheck, FileQuestion, Tags, Group, Download, Sliders, ChevronDown, Globe, CreditCard, ShieldCheck, Percent, Bell, Sun, Moon, Network, DollarSign, Building } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useLanguage } from "@/hooks/useLanguage";
import { useAuth } from "@/hooks/auth/AuthContext";
import { useSharedData } from "@/hooks/data/DataContext";
import { cn } from "@/lib/utils";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useState, useEffect } from "react";

export default function AppLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();
  const { t, isRTL } = useLanguage();
  const { role, logout, user, isAdmin } = useAuth();
  const { users } = useSharedData();
  
  const userName = user?.name || users?.find(u => u.id === user?.id)?.name || user?.email?.split('@')[0] || t("guest");
  const [isDarkMode, setIsDarkMode] = useState(
    document.documentElement.classList.contains('dark')
  );
  
  // Handle dark mode toggle
  const toggleDarkMode = () => {
    if (isDarkMode) {
      document.documentElement.classList.remove('dark');
      setIsDarkMode(false);
    } else {
      document.documentElement.classList.add('dark');
      setIsDarkMode(true);
    }
  };
  
  const handleLogout = async () => {
    await logout();
  };

  // Check if any sub-route of web-settings is active
  const isWebSettingsActive = location.pathname.includes('/web-settings');
  
  // Generate user initial for avatar
  const userInitial = userName ? userName.charAt(0).toUpperCase() : "U";
  
  // Get random pastel color based on username (for avatar background)
  const getAvatarColor = (name: string) => {
    const colors = [
      'bg-red-200 text-red-700',
      'bg-blue-200 text-blue-700', 
      'bg-green-200 text-green-700',
      'bg-yellow-200 text-yellow-700',
      'bg-purple-200 text-purple-700',
      'bg-pink-200 text-pink-700',
      'bg-indigo-200 text-indigo-700',
      'bg-teal-200 text-teal-700'
    ];
    
    // Simple hash function for consistent color selection
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    const index = Math.abs(hash % colors.length);
    return colors[index];
  };
  
  const avatarColor = getAvatarColor(userName);

  const menuItems = [{
    title: t("dashboard"),
    path: role === "distributor" ? "/distributor-dashboard" : "/dashboard",
    icon: Home,
    show: true
  }, {
    title: t("myCertFiles"),
    path: "/my-cert-files",
    icon: FileCheck,
    show: !isAdmin && role !== "distributor"
  }, {
    title: t("myUsers"),
    path: "/my-users",
    icon: Users,
    show: role === "distributor"
  }, {
    title: t("commissions"),
    path: "/commissions",
    icon: DollarSign,
    show: role === "distributor"
  }, {
    title: t("users"),
    path: "/users-manager",
    icon: Users,
    show: role === "admin"
  }, {
    title: t("distributorsManager") || "إدارة الموزعين",
    path: "/distributors-manager",
    icon: Network,
    show: role === "admin"
  }, {
    title: t("operations"),
    path: "/operations",
    icon: LineChart,
    show: true
  }, {
    title: t("discounts"),
    path: "/discounts",
    icon: Tags,
    show: role === "admin"
  }, {
    title: t("groupsManagement"),
    path: "/groups-management",
    icon: Group,
    show: role === "admin"
  }, {
    title: t("toolUpdate"),
    path: "/tool-update",
    icon: Download,
    show: role === "admin"
  }, {
    title: t("toolSettings"),
    path: "/tool-settings",
    icon: Sliders,
    show: role === "admin"
  }, {
    title: t("serverApiData"),
    path: "/server-api-data",
    icon: Database,
    show: role === "admin"
  }, {
    title: t("serverStorage"),
    path: "/server-storage",
    icon: FileQuestion,
    show: role === "admin"
  }, {
    title: t("settings"),
    path: "/settings",
    icon: Settings,
    show: true
  }].filter(item => item.show);

  // Web Settings submenu items with appropriate icons
  const webSettingsItems = [
    {
      title: t("supportedModels") || "Supported Models",
      path: "/web-settings/supported-models",
      icon: Globe // Globe icon for Supported Models
    }, 
    {
      title: t("pricing") || "Pricing",
      path: "/web-settings/pricing",
      icon: CreditCard // CreditCard icon for Pricing
    },
    {
      title: t("paymentMethods") || "Payment Methods",
      path: "/web-settings/payment-methods",
      icon: CreditCard // CreditCard icon for Payment Methods
    },
    {
      title: t("discountOffers") || "Discount Offers",
      path: "/web-settings/discount-offers", 
      icon: Percent // Percent icon for Discount Offers
    }
  ];
  
  // Reorder menuItems to insert WebSettings before Settings
  const getOrderedMenuItems = () => {
    const orderMenuItems = [...menuItems];
    // Find the index of the Settings item
    const settingsIndex = orderMenuItems.findIndex(item => item.path === "/settings");
    
    if (settingsIndex !== -1 && isAdmin) {
      // Create a WebSettings placeholder (just for display in the sidebar, not a real route)
      const webSettingsItem = {
        title: t("webSettings") || "Web Settings",
        path: "#", // Not used for navigation, since we use Accordion
        icon: Globe,
        show: true
      };
      
      // Insert WebSettings before Settings
      orderMenuItems.splice(settingsIndex, 0, webSettingsItem);
    }
    
    return orderMenuItems;
  };
  
  const orderedMenuItems = getOrderedMenuItems();

  return <SidebarProvider defaultOpen={!isMobile}>
      {/* Enhanced Top Navigation Bar with modern glass effect */}
      <div className="fixed top-0 left-0 right-0 h-16 backdrop-blur-xl bg-white/95 dark:bg-gray-950/95 border-b border-gray-200/50 dark:border-gray-800/50 shadow-lg shadow-gray-200/20 dark:shadow-gray-900/20 flex items-center justify-between px-6 z-50 transition-all duration-300">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-sm sm:text-base hover:bg-gray-100/80 dark:hover:bg-gray-800/80 p-2.5 rounded-xl transition-all duration-200 hover:scale-105 focus-ring nav-item" />

          <div className="flex items-center gap-3">
            {/* Logo/Brand */}
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/70 rounded-lg flex items-center justify-center shadow-md">
              <span className="text-white font-bold text-sm">P</span>
            </div>
            <h1 className="text-xl font-bold gradient-text">
              {t("pegasusTool")}
            </h1>
          </div>

          {/* Current page breadcrumb - enhanced design */}
          <div className="hidden lg:flex items-center">
            <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-3"></div>
            <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                {getCurrentPageTitle()}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Theme toggle with enhanced design */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleDarkMode}
            className="rounded-xl hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-all duration-200 hover:scale-105 p-2.5"
            aria-label={isDarkMode ? t("lightMode") : t("darkMode")}
          >
            {isDarkMode ? (
              <Sun className="h-5 w-5 text-yellow-500 transition-transform duration-200 hover:rotate-12" />
            ) : (
              <Moon className="h-5 w-5 text-blue-600 transition-transform duration-200 hover:-rotate-12" />
            )}
          </Button>

          {/* Notifications with modern badge */}
          <Button
            variant="ghost"
            size="icon"
            className="rounded-xl hover:bg-gray-100/80 dark:hover:bg-gray-800/80 relative transition-all duration-200 hover:scale-105 p-2.5"
          >
            <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-red-500 to-red-600 rounded-full border-2 border-white dark:border-gray-950 animate-pulse"></span>
          </Button>
          
          {/* Enhanced user section with modern design */}
          <div className="flex items-center gap-3">
            <div className="hidden md:flex flex-col items-end">
              <span className="text-sm font-semibold text-gray-700 dark:text-gray-200">
                {userName}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {role === "admin" ? t("administrator") : t("user")}
              </span>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-2 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 rounded-xl transition-all duration-200 hover:scale-105 p-2"
                >
                  <Avatar className="h-9 w-9 border-2 border-primary/30 shadow-md">
                    <AvatarImage src="" alt={userName} />
                    <AvatarFallback className={`${avatarColor} text-sm font-semibold`}>
                      {userInitial}
                    </AvatarFallback>
                  </Avatar>
                  <ChevronDown className="h-4 w-4 text-gray-500 dark:text-gray-400 transition-transform duration-200 group-hover:rotate-180" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-64 mt-2 animate-in slide-in-from-top-2 border border-gray-200/50 dark:border-gray-700/50 shadow-xl rounded-xl backdrop-blur-sm bg-white/95 dark:bg-gray-900/95"
              >
                <div className="px-4 py-4 border-b border-gray-100/50 dark:border-gray-800/50">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10 border-2 border-primary/20">
                      <AvatarImage src="" alt={userName} />
                      <AvatarFallback className={`${avatarColor} text-sm font-semibold`}>
                        {userInitial}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">{userName}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email}</p>
                      <p className="text-xs text-primary font-medium">{role === "admin" ? t("administrator") : t("user")}</p>
                    </div>
                  </div>
                </div>
                
                <div className="py-2">
                  <DropdownMenuItem
                    onClick={() => navigate("/edit-profile")}
                    className="cursor-pointer hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-all duration-200 flex items-center gap-3 py-3 mx-2 rounded-lg"
                  >
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                      <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="font-medium">{t("editProfile")}</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => navigate("/settings")}
                    className="cursor-pointer hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-all duration-200 flex items-center gap-3 py-3 mx-2 rounded-lg"
                  >
                    <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                      <Settings className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    </div>
                    <span className="font-medium">{t("settings")}</span>
                  </DropdownMenuItem>
                </div>

                <div className="border-t border-gray-100/50 dark:border-gray-800/50 py-2">
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="cursor-pointer text-red-600 dark:text-red-400 hover:bg-red-50/80 dark:hover:bg-red-900/30 transition-all duration-200 flex items-center gap-3 py-3 mx-2 rounded-lg"
                  >
                    <div className="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                      <LogOut className="h-4 w-4 text-red-600 dark:text-red-400" />
                    </div>
                    <span className="font-medium">{t("logout")}</span>
                  </DropdownMenuItem>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      
      <div className="flex min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-900 dark:to-gray-950 pt-16" dir={isRTL ? "rtl" : "ltr"}>
        <Sidebar side={isRTL ? "right" : "left"} variant={isMobile ? "floating" : "sidebar"} className="border-r border-gray-200/50 dark:border-gray-800/50 bg-white/95 dark:bg-gray-950/95 backdrop-blur-xl">
          <SidebarHeader className="flex flex-col items-center justify-center px-3 py-3 border-b border-gray-200/50 dark:border-gray-800/50 bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/5">
            <div className="flex items-center gap-3">
              <div className="w-9 h-9 bg-gradient-to-br from-primary to-primary/70 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-base">P</span>
              </div>
              <h1 className="text-lg font-bold gradient-text">{t("pegasusTool")}</h1>
            </div>
          </SidebarHeader>
          
          <SidebarContent className="px-3 py-3">
            <SidebarMenu className="space-y-1.5">
              {orderedMenuItems.map((item, index) => {
                // Check if the current item is WebSettings (use the path to identify it)
                if (item.path === "#" && isAdmin) {
                  return (
                    <SidebarMenuItem key="web-settings-section">
                      <Accordion type="single" collapsible className="w-full" defaultValue={isWebSettingsActive ? "web-settings" : undefined}>
                        <AccordionItem value="web-settings" className="border-none">
                          <AccordionTrigger className={cn(
                            "py-2.5 px-3 hover:bg-gray-100/80 dark:hover:bg-gray-800/80 rounded-lg text-sm group transition-all duration-300 relative",
                            isWebSettingsActive && "bg-primary/10 text-primary"
                          )}>
                            <div className="flex items-center gap-3 group-hover:translate-x-1 transition-all">
                              <div className={cn(
                                "w-7 h-7 rounded-md flex items-center justify-center transition-all duration-300",
                                isWebSettingsActive
                                  ? "bg-primary/20 text-primary"
                                  : "bg-gray-100 dark:bg-gray-800 group-hover:bg-primary/20 group-hover:text-primary"
                              )}>
                                <Globe className="h-4 w-4" />
                              </div>
                              <span className="font-medium group-hover:text-primary transition-colors">{t("webSettings") || "Web Settings"}</span>

                              {/* Active indicator for the parent menu */}
                              {isWebSettingsActive && (
                                <span className="absolute inset-y-0 right-2 w-1 bg-primary rounded-full"></span>
                              )}
                            </div>
                          </AccordionTrigger>
                          <AccordionContent className="pt-2 pb-0 animate-accordion-down">
                            <div className="flex flex-col space-y-1 pl-4">
                              {webSettingsItems.map(subItem => (
                                <SidebarMenuButton
                                  key={subItem.path}
                                  asChild
                                  className="py-2"
                                  isActive={location.pathname.includes(subItem.path)}
                                >
                                  <button
                                    onClick={() => navigate(subItem.path)}
                                    className={cn(
                                      "text-sm w-full text-left px-3 py-2 rounded-md flex items-center gap-3 transition-all duration-300 hover:translate-x-1 relative",
                                      location.pathname.includes(subItem.path)
                                        ? "bg-primary/15 text-primary font-medium shadow-sm"
                                        : "hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
                                    )}
                                  >
                                    <div className={cn(
                                      "w-5 h-5 rounded flex items-center justify-center transition-all",
                                      location.pathname.includes(subItem.path)
                                        ? "bg-primary/20 text-primary"
                                        : "bg-gray-100 dark:bg-gray-800"
                                    )}>
                                      <subItem.icon className="h-3 w-3 flex-shrink-0" />
                                    </div>
                                    <span className="font-medium">{subItem.title}</span>
                                    {location.pathname.includes(subItem.path) && (
                                      <span className="absolute inset-y-0 right-2 w-0.5 bg-primary rounded-full"></span>
                                    )}
                                  </button>
                                </SidebarMenuButton>
                              ))}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </SidebarMenuItem>
                  );
                }
                
                return (
                  <SidebarMenuItem key={item.path}>
                    <SidebarMenuButton asChild isActive={location.pathname === item.path} tooltip={item.title}>
                      <button
                        onClick={() => navigate(item.path)}
                        className={cn(
                          "w-full text-sm relative group transition-all duration-300 hover:translate-x-1 px-3 py-2.5 rounded-lg flex items-center gap-3",
                          location.pathname === item.path
                            ? "bg-primary/10 text-primary font-medium shadow-sm"
                            : "hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
                        )}
                      >
                        <div className={cn(
                          "w-7 h-7 rounded-md flex items-center justify-center transition-all duration-300",
                          location.pathname === item.path
                            ? "bg-primary/20 text-primary"
                            : "bg-gray-100 dark:bg-gray-800 group-hover:bg-primary/20 group-hover:text-primary"
                        )}>
                          <item.icon className="h-4 w-4" />
                        </div>
                        <span className="font-medium group-hover:text-primary transition-colors">{item.title}</span>

                        {/* Active indicator */}
                        {location.pathname === item.path && (
                          <span className="absolute inset-y-0 right-2 w-1 bg-primary rounded-full"></span>
                        )}
                      </button>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarContent>
          
          <SidebarFooter className="px-4 py-3 border-t border-gray-200/50 dark:border-gray-800/50 bg-gradient-to-r from-gray-50/50 to-gray-100/50 dark:from-gray-900/50 dark:to-gray-950/50">
            <div className="text-center space-y-1">
             <p className="text-xs text-gray-400 dark:text-gray-500 leading-tight">
                {t("allRightsReserved") || "جميع الحقوق محفوظة"}
              </p>
            </div>
          </SidebarFooter>
        </Sidebar>

        <main className="flex-1 p-6 overflow-auto bg-gradient-to-br from-gray-50/50 to-white dark:from-gray-900/50 dark:to-gray-950 dark:text-white">
          {/* Enhanced main content with smooth animations */}
          <div className="animate-in fade-in-50 slide-in-from-bottom-4 duration-500">
            {children}
          </div>
        </main>
      </div>
    </SidebarProvider>;
    
    // Helper function to get current page title
    function getCurrentPageTitle() {
      const currentPath = location.pathname;
      if (currentPath.includes('/web-settings')) {
        return t("webSettings") || "Web Settings";
      }
      const menuItem = menuItems.find(item => item.path === currentPath);
      return menuItem ? menuItem.title : t("dashboard");
    }
}
