import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLanguage } from "@/hooks/useLanguage";
import { useDistributorOperations } from "@/hooks/useDistributorOperations";
import { usePricingPlans } from "@/hooks/usePricingPlans";
import { toast } from "@/components/ui/sonner";
import { User, Mail, Phone, MapPin, CreditCard, Calendar } from "lucide-react";

interface AddUserDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// Countries list (same as admin dialog)
const countries = [
  "Saudi Arabia", "United Arab Emirates", "Kuwait", "Qatar", "Bahrain", "Oman",
  "Egypt", "Jordan", "Lebanon", "Syria", "Iraq", "Palestine", "Morocco",
  "Algeria", "Tunisia", "Libya", "Sudan", "Yemen", "Somalia", "Djibouti",
  "Comoros", "Mauritania", "United States", "Canada", "United Kingdom",
  "Germany", "France", "Italy", "Spain", "Netherlands", "Belgium", "Switzerland",
  "Austria", "Sweden", "Norway", "Denmark", "Finland", "Poland", "Czech Republic",
  "Hungary", "Romania", "Bulgaria", "Greece", "Portugal", "Ireland", "Luxembourg",
  "Malta", "Cyprus", "Estonia", "Latvia", "Lithuania", "Slovenia", "Slovakia",
  "Croatia", "Serbia", "Montenegro", "Bosnia and Herzegovina", "North Macedonia",
  "Albania", "Moldova", "Ukraine", "Belarus", "Russia", "Turkey", "Georgia",
  "Armenia", "Azerbaijan", "Kazakhstan", "Uzbekistan", "Turkmenistan", "Kyrgyzstan",
  "Tajikistan", "Afghanistan", "Pakistan", "India", "Bangladesh", "Sri Lanka",
  "Nepal", "Bhutan", "Maldives", "Myanmar", "Thailand", "Laos", "Cambodia",
  "Vietnam", "Malaysia", "Singapore", "Brunei", "Philippines", "Indonesia",
  "East Timor", "Papua New Guinea", "Australia", "New Zealand", "Fiji",
  "Solomon Islands", "Vanuatu", "Samoa", "Tonga", "Tuvalu", "Kiribati",
  "Nauru", "Palau", "Marshall Islands", "Micronesia", "China", "Japan",
  "South Korea", "North Korea", "Mongolia", "Taiwan", "Hong Kong", "Macau",
  "Brazil", "Argentina", "Chile", "Peru", "Colombia", "Venezuela", "Ecuador",
  "Bolivia", "Paraguay", "Uruguay", "Guyana", "Suriname", "French Guiana",
  "Mexico", "Guatemala", "Belize", "El Salvador", "Honduras", "Nicaragua",
  "Costa Rica", "Panama", "Cuba", "Jamaica", "Haiti", "Dominican Republic",
  "Trinidad and Tobago", "Barbados", "Saint Lucia", "Grenada", "Saint Vincent and the Grenadines",
  "Antigua and Barbuda", "Dominica", "Saint Kitts and Nevis", "Bahamas",
  "Nigeria", "Kenya", "Ethiopia", "Uganda", "Tanzania", "Ghana", "Cameroon",
  "Ivory Coast", "Madagascar", "Burkina Faso", "Mali", "Malawi", "Niger",
  "Zambia", "Senegal", "Chad", "Guinea", "Rwanda", "Benin", "Burundi",
  "Tunisia", "South Sudan", "Togo", "Sierra Leone", "Liberia", "Central African Republic",
  "Mauritius", "Eswatini", "Gambia", "Botswana", "Gabon", "Lesotho",
  "Namibia", "Guinea-Bissau", "Equatorial Guinea", "Mauritania", "Djibouti",
  "Comoros", "Cape Verde", "Sao Tome and Principe", "Seychelles", "South Africa"
];

// Generate random name function
const generateRandomName = () => {
  const firstNames = ["أحمد", "محمد", "علي", "حسن", "عبدالله", "خالد", "سعد", "فهد", "عبدالعزيز", "سلطان"];
  const lastNames = ["العتيبي", "الغامدي", "القحطاني", "الزهراني", "الشهري", "العنزي", "الدوسري", "المطيري", "الحربي", "الشمري"];
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
  return `${firstName} ${lastName}`;
};

// Generate random phone function
const generateRandomPhone = () => {
  const prefixes = ["050", "053", "054", "055", "056", "057", "058", "059"];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const number = Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
  return `+966${prefix}${number}`;
};

export function AddUserDialog({ isOpen, onClose, onSuccess }: AddUserDialogProps) {
  const { t, isRTL } = useLanguage();
  const { createUser } = useDistributorOperations();
  const { plans, loading: plansLoading } = usePricingPlans();

  const [name, setName] = useState(generateRandomName());
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [credits, setCredits] = useState("0");
  const [userType, setUserType] = useState("Credits License");
  const [phone, setPhone] = useState(generateRandomPhone());
  const [country, setCountry] = useState("Saudi Arabia");
  const [subscriptionMonths, setSubscriptionMonths] = useState("3");
  const [selectedPlan, setSelectedPlan] = useState("free");
  const [showSubscriptionMonths, setShowSubscriptionMonths] = useState(false);
  const [loading, setLoading] = useState(false);

  // Update subscription months visibility when user type changes
  useEffect(() => {
    setShowSubscriptionMonths(userType === "Monthly License");
  }, [userType]);

  // Calculate expiry date and plan name
  const calculateExpiryAndPlan = () => {
    let expiryDate = "";
    let planName = "";

    if (userType === "Monthly License") {
      const months = parseInt(subscriptionMonths);
      const expiry = new Date();
      expiry.setMonth(expiry.getMonth() + months);
      expiryDate = expiry.toISOString().split('T')[0];

      // Find plan name
      const plan = plans.find(p => p.id === selectedPlan);
      planName = plan ? plan.name : "Free Plan";
    } else {
      // For Credits License, use free plan
      planName = "Free Plan";
    }

    const startDate = new Date().toISOString().split('T')[0];
    return { expiryDate, planName, startDate };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim() || !email.trim() || !password.trim()) {
      toast.error(t("fillRequiredFields") || "يرجى ملء جميع الحقول المطلوبة");
      return;
    }

    if (password.length < 8) {
      toast.error(t("passwordMinLength") || "كلمة المرور يجب أن تكون 8 أحرف على الأقل");
      return;
    }

    const { expiryDate, planName } = calculateExpiryAndPlan();
    const formattedCredits = credits + ".0";

    const newUser = {
      Name: name,
      Email: email,
      Password: password,
      Credits: formattedCredits,
      User_Type: userType,
      Phone: phone,
      Country: country,
      Activate: "Active",
      Block: "Not Blocked",
      Start_Date: new Date().toISOString().split('T')[0],
      Expiry_Time: expiryDate,
      Email_Type: "User",
      Hwid: "Null",
      My_Plans: planName
    };

    try {
      setLoading(true);

      // Use the distributor's createUser function but with admin-style data
      await createUser({
        name: newUser.Name,
        email: newUser.Email,
        password: newUser.Password,
        phone: newUser.Phone,
        country: newUser.Country,
        userType: newUser.User_Type,
        credits: newUser.Credits,
        expiryTime: newUser.Expiry_Time
      });

      // Reset form fields with new random data
      setName(generateRandomName());
      setEmail("");
      setPassword("");
      setCredits("0");
      setUserType("Credits License");
      setPhone(generateRandomPhone());
      setCountry("Saudi Arabia");
      setSubscriptionMonths("3");
      setSelectedPlan("free");

      onSuccess();
    } catch (error) {
      console.log("Error in handleSubmit:", error);
      toast.error(t("error") || "خطأ", {
        description: t("addUserError") || "فشل في إضافة المستخدم"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto" dir={isRTL ? "rtl" : "ltr"}>
        <DialogHeader>
          <DialogTitle>{t("addUser") || "إضافة مستخدم"}</DialogTitle>
          <DialogDescription>
            {t("addUserDescription") || "إضافة مستخدم جديد وتعيينه لك"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="grid gap-2">
              <Label htmlFor="name">{t("name") || "الاسم"}</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder={t("enterName") || "أدخل الاسم"}
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="email">{t("email") || "البريد الإلكتروني"}</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={t("enterEmail") || "أدخل البريد الإلكتروني"}
                required
              />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="grid gap-2">
              <Label htmlFor="password">{t("password") || "كلمة المرور"}</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder={t("enterPassword") || "أدخل كلمة المرور"}
                required
                minLength={8}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="phone">{t("phone") || "رقم الهاتف"}</Label>
              <Input
                id="phone"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                placeholder={t("enterPhone") || "أدخل رقم الهاتف"}
              />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="grid gap-2">
              <Label htmlFor="userType">{t("userType") || "نوع المستخدم"}</Label>
              <Select value={userType} onValueChange={setUserType}>
                <SelectTrigger id="userType">
                  <SelectValue placeholder={t("selectUserType") || "اختر نوع المستخدم"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Credits License">{t("creditsLicense") || "ترخيص رصيد"}</SelectItem>
                  <SelectItem value="Monthly License">{t("monthlyLicense") || "ترخيص شهري"}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="credits">{t("credits") || "الرصيد"}</Label>
              <Input
                id="credits"
                type="number"
                value={credits}
                onChange={(e) => setCredits(e.target.value)}
                placeholder="0"
                min="0"
              />
            </div>
          </div>

          {showSubscriptionMonths && (
            <div className="grid gap-4 md:grid-cols-2">
              <div className="grid gap-2">
                <Label htmlFor="subscriptionMonths">{t("subscriptionMonths") || "أشهر الاشتراك"}</Label>
                <Select value={subscriptionMonths} onValueChange={setSubscriptionMonths}>
                  <SelectTrigger id="subscriptionMonths">
                    <SelectValue placeholder={t("selectMonths") || "اختر عدد الأشهر"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 {t("month") || "شهر"}</SelectItem>
                    <SelectItem value="3">3 {t("months") || "أشهر"}</SelectItem>
                    <SelectItem value="6">6 {t("months") || "أشهر"}</SelectItem>
                    <SelectItem value="12">12 {t("month") || "شهر"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="plan">{t("plan") || "الخطة"}</Label>
                <Select value={selectedPlan} onValueChange={setSelectedPlan} disabled={plansLoading}>
                  <SelectTrigger id="plan">
                    <SelectValue placeholder={t("selectPlan") || "اختر الخطة"} />
                  </SelectTrigger>
                  <SelectContent>
                    {plans.map(plan => (
                      <SelectItem key={plan.id} value={plan.id}>
                        {plan.name} - {plan.price} {t("currency") || "ريال"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <div className="grid gap-2">
            <Label htmlFor="country">{t("country") || "الدولة"}</Label>
            <Select value={country} onValueChange={setCountry}>
              <SelectTrigger id="country">
                <SelectValue placeholder={t("selectCountry") || "اختر الدولة"} />
              </SelectTrigger>
              <SelectContent>
                {countries.map(countryName => (
                  <SelectItem key={countryName} value={countryName}>
                    {countryName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter className="sm:justify-start">
            <Button type="submit" disabled={loading || plansLoading}>
              {loading ? (t("adding") || "جاري الإضافة...") : (t("addUser") || "إضافة المستخدم")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
